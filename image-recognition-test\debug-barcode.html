<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条码数据调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>条码数据调试工具</h1>
        
        <div class="section">
            <h3>输入 TextIn API 响应数据</h3>
            <p>请粘贴包含条码的完整 TextIn API 响应数据：</p>
            <textarea id="apiResponse" placeholder="粘贴 TextIn API 响应 JSON 数据..."></textarea>
            <br>
            <button onclick="analyzeData()">分析条码数据</button>
            <button onclick="loadSampleData()">加载示例数据</button>
        </div>

        <div class="section">
            <h3>分析结果</h3>
            <div id="analysisResult"></div>
        </div>

        <div class="section">
            <h3>详细数据结构</h3>
            <pre id="detailOutput"></pre>
        </div>
    </div>

    <script>
        function analyzeData() {
            const input = document.getElementById('apiResponse').value.trim();
            const resultDiv = document.getElementById('analysisResult');
            const detailDiv = document.getElementById('detailOutput');
            
            if (!input) {
                resultDiv.innerHTML = '<div class="error">请输入 TextIn API 响应数据</div>';
                return;
            }
            
            try {
                const data = JSON.parse(input);
                
                // 查找所有可能的条码数据
                const barcodeData = findBarcodeData(data);
                
                if (barcodeData.length === 0) {
                    resultDiv.innerHTML = '<div class="error">未找到条码数据</div>';
                    detailDiv.textContent = JSON.stringify(data, null, 2);
                    return;
                }
                
                // 显示分析结果
                let html = '<div class="success">找到 ' + barcodeData.length + ' 个条码数据:</div>';
                
                barcodeData.forEach((item, index) => {
                    html += `<div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">`;
                    html += `<h4>条码 ${index + 1}:</h4>`;
                    html += `<p><strong>位置:</strong> ${item.location}</p>`;
                    html += `<p><strong>类型:</strong> ${item.sub_type || 'unknown'}</p>`;
                    html += `<p><strong>可能的内容字段:</strong></p>`;
                    html += `<ul>`;
                    
                    const fields = ['text', 'data.text', 'content', 'value', 'code'];
                    fields.forEach(field => {
                        const value = getNestedValue(item.data, field);
                        if (value !== undefined && value !== null) {
                            html += `<li><span class="highlight">${field}</span>: "${value}"</li>`;
                        }
                    });
                    
                    html += `</ul>`;
                    html += `</div>`;
                });
                
                resultDiv.innerHTML = html;
                detailDiv.textContent = JSON.stringify(barcodeData, null, 2);
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="error">JSON 解析失败: ' + error.message + '</div>';
                detailDiv.textContent = '';
            }
        }
        
        function findBarcodeData(data) {
            const results = [];
            
            // 递归查找条码数据
            function searchObject(obj, path = '') {
                if (typeof obj !== 'object' || obj === null) return;
                
                if (Array.isArray(obj)) {
                    obj.forEach((item, index) => {
                        searchObject(item, `${path}[${index}]`);
                    });
                } else {
                    // 检查是否是条码对象
                    if (obj.sub_type === 'barcode' || obj.type === 'barcode' || 
                        (obj.type === 'image' && obj.sub_type === 'barcode')) {
                        results.push({
                            location: path,
                            data: obj,
                            sub_type: obj.sub_type,
                            type: obj.type
                        });
                    }
                    
                    // 继续递归搜索
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            searchObject(obj[key], path ? `${path}.${key}` : key);
                        }
                    }
                }
            }
            
            searchObject(data);
            return results;
        }
        
        function getNestedValue(obj, path) {
            return path.split('.').reduce((current, key) => {
                return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
        }
        
        function loadSampleData() {
            const sampleData = {
                "result": {
                    "pages": [{
                        "content": [{
                            "id": 0,
                            "type": "image",
                            "sub_type": "barcode",
                            "text": "18818683102",
                            "data": {
                                "text": "18818683102",
                                "base64": "..."
                            }
                        }]
                    }]
                }
            };
            
            document.getElementById('apiResponse').value = JSON.stringify(sampleData, null, 2);
        }
    </script>
</body>
</html>
