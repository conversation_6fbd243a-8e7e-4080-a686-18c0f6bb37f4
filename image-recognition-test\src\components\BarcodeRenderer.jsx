import React, { useEffect, useRef } from 'react';
import JsBarcode from 'jsbarcode';

const BarcodeRenderer = ({ 
  value = '123456789', 
  format = 'CODE128', 
  width = 2, 
  height = 100, 
  displayValue = true,
  fontSize = 20,
  textAlign = 'center',
  textPosition = 'bottom',
  background = '#ffffff',
  lineColor = '#000000',
  style = {}
}) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    if (canvasRef.current && value) {
      try {
        // 清除之前的内容
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 生成条码
        JsBarcode(canvas, value, {
          format: format,
          width: width,
          height: height,
          displayValue: displayValue,
          fontSize: fontSize,
          textAlign: textAlign,
          textPosition: textPosition,
          background: background,
          lineColor: lineColor,
          margin: 0
        });
      } catch (error) {
        console.error('条码生成失败:', error);
        
        // 如果生成失败，显示错误信息
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('条码生成失败', canvas.width / 2, canvas.height / 2);
      }
    }
  }, [value, format, width, height, displayValue, fontSize, textAlign, textPosition, background, lineColor]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        objectFit: 'contain',
        ...style
      }}
    />
  );
};

export default BarcodeRenderer;
