<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加粗文本解析测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .test-output {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .fragment {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: #fff;
        }
        .fragment.bold {
            font-weight: bold;
            background: #ffe6e6;
        }
        .fragment.italic {
            font-style: italic;
            background: #e6f3ff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>加粗文本解析测试</h1>
        
        <div class="test-section">
            <h3>测试用例 1: 完全加粗的文本</h3>
            <div class="test-input">输入: "**解冻标签**"</div>
            <button onclick="testParsing('**解冻标签**', 'result1')">测试</button>
            <div id="result1" class="test-output"></div>
        </div>

        <div class="test-section">
            <h3>测试用例 2: 混合样式文本</h3>
            <div class="test-input">输入: "普通文本 **加粗文本** 更多普通文本"</div>
            <button onclick="testParsing('普通文本 **加粗文本** 更多普通文本', 'result2')">测试</button>
            <div id="result2" class="test-output"></div>
        </div>

        <div class="test-section">
            <h3>测试用例 3: 斜体文本</h3>
            <div class="test-input">输入: "普通文本 *斜体文本* 更多普通文本"</div>
            <button onclick="testParsing('普通文本 *斜体文本* 更多普通文本', 'result3')">测试</button>
            <div id="result3" class="test-output"></div>
        </div>

        <div class="test-section">
            <h3>测试用例 4: 多个加粗片段</h3>
            <div class="test-input">输入: "**第一个** 普通 **第二个** 文本"</div>
            <button onclick="testParsing('**第一个** 普通 **第二个** 文本', 'result4')">测试</button>
            <div id="result4" class="test-output"></div>
        </div>

        <div class="test-section">
            <h3>测试用例 5: 纯普通文本</h3>
            <div class="test-input">输入: "品名："</div>
            <button onclick="testParsing('品名：', 'result5')">测试</button>
            <div id="result5" class="test-output"></div>
        </div>
    </div>

    <script type="module">
        // 导入 DataConverter 类
        import { DataConverter } from './src/services/dataConverter.js';
        
        const converter = new DataConverter();
        
        // 将测试函数暴露到全局作用域
        window.testParsing = function(text, resultId) {
            try {
                const fragments = converter.parseMarkdownText(text);
                
                const resultDiv = document.getElementById(resultId);
                resultDiv.innerHTML = '';
                
                // 显示解析结果
                const resultText = document.createElement('div');
                resultText.innerHTML = '<strong>解析结果:</strong>';
                resultDiv.appendChild(resultText);
                
                const fragmentsContainer = document.createElement('div');
                fragmentsContainer.style.marginTop = '10px';
                
                fragments.forEach((fragment, index) => {
                    const span = document.createElement('span');
                    span.className = 'fragment';
                    if (fragment.bold) span.classList.add('bold');
                    if (fragment.italic) span.classList.add('italic');
                    span.textContent = `"${fragment.text}"`;
                    fragmentsContainer.appendChild(span);
                });
                
                resultDiv.appendChild(fragmentsContainer);
                
                // 显示 JSON 数据
                const jsonDiv = document.createElement('div');
                jsonDiv.style.marginTop = '10px';
                jsonDiv.innerHTML = '<strong>JSON 数据:</strong>';
                const pre = document.createElement('pre');
                pre.style.background = '#f8f9fa';
                pre.style.padding = '10px';
                pre.style.borderRadius = '4px';
                pre.style.fontSize = '12px';
                pre.textContent = JSON.stringify(fragments, null, 2);
                jsonDiv.appendChild(pre);
                resultDiv.appendChild(jsonDiv);
                
            } catch (error) {
                const resultDiv = document.getElementById(resultId);
                resultDiv.innerHTML = `<div style="color: red;">错误: ${error.message}</div>`;
            }
        };
    </script>
</body>
</html>
