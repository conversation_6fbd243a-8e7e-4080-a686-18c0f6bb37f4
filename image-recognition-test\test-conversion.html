<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TextIn 数据转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .input-section {
            background: #f8f9fa;
        }
        .output-section {
            background: #e8f5e8;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .element {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: #fff;
        }
        .element.bold {
            font-weight: bold;
            background: #ffe6e6;
        }
        .element-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TextIn 数据转换测试</h1>
        
        <div class="section input-section">
            <h3>输入数据 (TextIn API 响应)</h3>
            <p>使用实际的 TextIn API 响应数据，包含 "**解冻标签**" 等加粗文本</p>
            <button onclick="loadTestData()">加载测试数据</button>
            <button onclick="loadBarcodeTestData()">加载条码测试数据</button>
            <button onclick="loadTableTestData()">加载表格测试数据</button>
            <button onclick="testConversion()">执行转换</button>
        </div>

        <div class="section output-section">
            <h3>转换结果 (XPrinter 格式)</h3>
            <div id="conversionResult"></div>
        </div>

        <div class="section">
            <h3>详细输出 (JSON)</h3>
            <pre id="jsonOutput"></pre>
        </div>
    </div>

    <script type="module">
        import { DataConverter } from './src/services/dataConverter.js';
        
        let testData = null;
        const converter = new DataConverter();
        
        // 加载测试数据
        window.loadTestData = async function() {
            try {
                const response = await fetch('./test-data.json');
                testData = await response.json();

                document.getElementById('jsonOutput').textContent =
                    '测试数据已加载:\n' + JSON.stringify(testData, null, 2);

                console.log('测试数据已加载:', testData);
            } catch (error) {
                console.error('加载测试数据失败:', error);
                document.getElementById('jsonOutput').textContent =
                    '加载测试数据失败: ' + error.message;
            }
        };

        // 加载条码测试数据
        window.loadBarcodeTestData = async function() {
            try {
                const response = await fetch('./test-barcode-data.json');
                testData = await response.json();

                document.getElementById('jsonOutput').textContent =
                    '条码测试数据已加载:\n' + JSON.stringify(testData, null, 2);

                console.log('条码测试数据已加载:', testData);
            } catch (error) {
                console.error('加载条码测试数据失败:', error);
                document.getElementById('jsonOutput').textContent =
                    '加载条码测试数据失败: ' + error.message;
            }
        };

        // 加载表格测试数据
        window.loadTableTestData = async function() {
            try {
                const response = await fetch('./test-table-data.json');
                testData = await response.json();

                document.getElementById('jsonOutput').textContent =
                    '表格测试数据已加载:\n' + JSON.stringify(testData, null, 2);

                console.log('表格测试数据已加载:', testData);
            } catch (error) {
                console.error('加载表格测试数据失败:', error);
                document.getElementById('jsonOutput').textContent =
                    '加载表格测试数据失败: ' + error.message;
            }
        };
        
        // 执行转换测试
        window.testConversion = function() {
            if (!testData) {
                alert('请先加载测试数据');
                return;
            }
            
            try {
                // 设置转换参数
                const options = {
                    canvasWidthMm: 40,
                    canvasHeightMm: 30
                };
                
                console.log('开始转换，参数:', options);
                console.log('输入数据:', testData);
                
                // 执行转换
                const result = converter.convertToXPrinter(testData, options);
                
                console.log('转换结果:', result);
                
                // 显示转换结果
                displayConversionResult(result);
                
                // 显示 JSON 输出
                document.getElementById('jsonOutput').textContent = 
                    JSON.stringify(result, null, 2);
                    
            } catch (error) {
                console.error('转换失败:', error);
                document.getElementById('conversionResult').innerHTML = 
                    `<div style="color: red;">转换失败: ${error.message}</div>`;
                document.getElementById('jsonOutput').textContent = 
                    '转换失败: ' + error.message;
            }
        };
        
        // 显示转换结果
        function displayConversionResult(elements) {
            const resultDiv = document.getElementById('conversionResult');
            resultDiv.innerHTML = '';
            
            elements.forEach((element, index) => {
                const elementDiv = document.createElement('div');
                elementDiv.className = 'element';
                
                if (element.bold === 'true') {
                    elementDiv.classList.add('bold');
                }
                
                let content = '';
                if (element.type === '1') { // 文本元素
                    content = `文本: "${element.content}"`;
                } else if (element.type === '2') { // 二维码
                    content = `二维码: "${element.content}"`;
                } else if (element.type === '3') { // 条形码
                    content = `条形码: "${element.content}"`;
                } else if (element.type === '4') { // 图片
                    content = `图片`;
                } else if (element.type === '5') { // 画布
                    content = `画布 (${element.width}mm × ${element.height}mm)`;
                } else {
                    content = `未知类型: ${element.type}`;
                }
                
                elementDiv.innerHTML = content;
                
                const infoDiv = document.createElement('div');
                infoDiv.className = 'element-info';
                infoDiv.innerHTML = `
                    类型: ${element.type}, 
                    位置: (${element.x}, ${element.y}), 
                    尺寸: ${element.width} × ${element.height}, 
                    加粗: ${element.bold || 'false'}
                `;
                elementDiv.appendChild(infoDiv);
                
                resultDiv.appendChild(elementDiv);
            });
        }
        
        // 页面加载时自动加载测试数据
        window.addEventListener('load', () => {
            loadTestData();
        });
    </script>
</body>
</html>
