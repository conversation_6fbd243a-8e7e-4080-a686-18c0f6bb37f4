/**
 * 坐标转换工具类
 * 用于将像素坐标转换为毫米坐标
 */
class CoordinateConverter {
  constructor() {
    this.canvasWidthMm = 0;   // 画布宽度（毫米）
    this.canvasHeightMm = 0;  // 画布高度（毫米）
    this.imageWidthPx = 0;    // 图像宽度（像素）
    this.imageHeightPx = 0;   // 图像高度（像素）
    this.scaleX = 1;          // X轴缩放比例
    this.scaleY = 1;          // Y轴缩放比例
  }

  /**
   * 设置转换参数
   * @param {number} canvasWidthMm - 画布宽度（毫米）
   * @param {number} canvasHeightMm - 画布高度（毫米）
   * @param {number} imageWidthPx - 图像宽度（像素）
   * @param {number} imageHeightPx - 图像高度（像素）
   */
  setConversionParams(canvasWidthMm, canvasHeightMm, imageWidthPx, imageHeightPx) {
    this.canvasWidthMm = canvasWidthMm;
    this.canvasHeightMm = canvasHeightMm;
    this.imageWidthPx = imageWidthPx;
    this.imageHeightPx = imageHeightPx;

    // 计算缩放比例：毫米/像素
    this.scaleX = canvasWidthMm / imageWidthPx;
    this.scaleY = canvasHeightMm / imageHeightPx;

    console.log('坐标转换参数设置:', {
      canvasWidthMm,
      canvasHeightMm,
      imageWidthPx,
      imageHeightPx,
      scaleX: this.scaleX,
      scaleY: this.scaleY
    });
  }

  /**
   * 将像素坐标转换为毫米坐标
   * @param {number} x - X坐标（像素）
   * @param {number} y - Y坐标（像素）
   * @returns {Object} 转换后的坐标 {x, y}（毫米）
   */
  pxToMm(x, y) {
    return {
      x: x * this.scaleX,
      y: y * this.scaleY
    };
  }

  /**
   * 将像素尺寸转换为毫米尺寸
   * @param {number} width - 宽度（像素）
   * @param {number} height - 高度（像素）
   * @returns {Object} 转换后的尺寸 {width, height}（毫米）
   */
  pxSizeToMm(width, height) {
    return {
      width: width * this.scaleX,
      height: height * this.scaleY
    };
  }

  /**
   * 转换边界框
   * @param {Object} bounds - 像素边界框 {x, y, width, height}
   * @returns {Object} 毫米边界框 {x, y, width, height}
   */
  convertBounds(bounds) {
    const position = this.pxToMm(bounds.x, bounds.y);
    const size = this.pxSizeToMm(bounds.width, bounds.height);

    return {
      x: Math.round(position.x * 100) / 100, // 保留2位小数
      y: Math.round(position.y * 100) / 100,
      width: Math.round(size.width * 100) / 100,
      height: Math.round(size.height * 100) / 100
    };
  }

  /**
   * 批量转换坐标点
   * @param {Array} points - 坐标点数组 [x1,y1,x2,y2,...]
   * @returns {Array} 转换后的坐标点数组（毫米）
   */
  convertPoints(points) {
    const result = [];
    for (let i = 0; i < points.length; i += 2) {
      const converted = this.pxToMm(points[i], points[i + 1]);
      result.push(Math.round(converted.x * 100) / 100);
      result.push(Math.round(converted.y * 100) / 100);
    }
    return result;
  }

  /**
   * 获取当前转换比例信息
   * @returns {Object} 转换比例信息
   */
  getConversionInfo() {
    return {
      canvasSizeMm: {
        width: this.canvasWidthMm,
        height: this.canvasHeightMm
      },
      imageSizePx: {
        width: this.imageWidthPx,
        height: this.imageHeightPx
      },
      scale: {
        x: this.scaleX,
        y: this.scaleY
      },
      ratio: `1px = ${Math.round(this.scaleX * 10000) / 10000}mm`
    };
  }

  /**
   * 验证转换参数是否有效
   * @returns {boolean} 是否有效
   */
  isValid() {
    return this.canvasWidthMm > 0 &&
      this.canvasHeightMm > 0 &&
      this.imageWidthPx > 0 &&
      this.imageHeightPx > 0;
  }
}

// 创建单例实例
const coordinateConverter = new CoordinateConverter();

export default coordinateConverter; 